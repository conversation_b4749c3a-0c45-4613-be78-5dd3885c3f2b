<script setup lang="ts">
import { ref } from 'vue'
import Autoplay from 'embla-carousel-autoplay'
import { Card, CardContent } from '@/components/ui/card'
import {
	Carousel, type CarouselApi,
	CarouselContent,
	CarouselItem,
} from '@/components/ui/carousel'

interface Source {
	lg?: string
	md?: string
	sm?: string
	titleClass?: string
	subtitleClass?: string
}

interface Item {
	source: Source
}

const sliderMainApi = ref<CarouselApi>()
const selectedIndex = ref(0)

const img = useImage()

/**
 * On a small image clicked
 * @param index
 */
const onThumbClick = (index: number) => {
	if (!sliderMainApi.value) {
		return
	}

	sliderMainApi.value.scrollTo(index)
	selectedIndex.value = sliderMainApi.value.selectedScrollSnap()
}

/**
 * Watch on the slider
 */
const handleOnChange = (mainApi: CarouselApi) => {
	selectedIndex.value = mainApi.selectedScrollSnap()
}

onMounted(() => {
	if (sliderMainApi.value) {
		sliderMainApi.value.on('select', handleOnChange)
	}
})

const plugin = Autoplay({
	delay: 2000,
	stopOnMouseEnter: true,
	stopOnInteraction: false,
})

const list: Item[] = [
	{
		source: {
			lg: '/banner/1.jpg',
			md: '/banner/1.jpg',
			sm: '/banner/1.jpg',
			titleClass: 'text-gray-600',
			subtitleClass: 'text-gray-500',
		},
	},
	{
		source: {
			lg: '/banner/2.jpg',
			md: '/banner/2.jpg',
			sm: '/banner/2.jpg',
			titleClass: 'text-white',
			subtitleClass: 'text-white',
		},
	},
	{
		source: {
			lg: '/banner/3.jpg',
			md: '/banner/3.jpg',
			sm: '/banner/3.jpg',
			titleClass: 'text-gray-600',
			subtitleClass: 'text-gray-500',
		},
	},
	{
		source: {
			lg: '/banner/4.jpg',
			md: '/banner/4.jpg',
			sm: '/banner/5.jpg',
			titleClass: 'text-gray-600',
			subtitleClass: 'text-gray-500',
		},
	},
]

// Preload the first hero image
const firstSlide = list[0]
useHead({
	link: [
		{
			rel: 'preload',
			as: 'image',
			href: img(firstSlide.source.lg, { width: 1800 }),
			imagesrcset: `${img(firstSlide.source.sm, { width: 400 })} 400w, ${img(firstSlide.source.sm, { width: 800 })} 800w, ${img(firstSlide.source.md, { width: 1200 })} 1200w, ${img(firstSlide.source.lg, { width: 1800 })} 1800w`,
			imagesizes: '(max-width: 599px) 100vw, (max-width: 991px) 100vw, 100vw',
		},
	],
})
</script>

<template>
	<div class="w-full sm:w-auto col-span-3 shadow">
		<Carousel
			class="relative w-full"
			:opts="{
				startIndex: 0,
				loop: true,
			}"
			:plugins="[plugin]"
			@init-api="(val) => sliderMainApi = val"
			@slide="handleOnChange"
			@mouseenter="plugin.stop"
			@mouseleave="plugin.timeUntilNext"
		>
			<CarouselContent>
				<CarouselItem
					v-for="(slide, index) in list"
					:id="`slide-${index}`"
					:key="`slide-${index}`"
					class="!p-0"
				>
					<Card class="p-0">
						<CardContent class="relative flex aspect-video w-full !p-0 sm:h-96 xs:h-44">
							<div class="flex absolute w-full h-full z-1">
								<div class="flex flex-col gap-4 xs:p-4 sm:ps-20 sm:pt-12">
									<h2
										class="font-bold max-w-96 leading-normal text-md sm:text-4xl drop-shadow-lg "
										:class="slide.source.titleClass"
									>
										{{ $t('home.hero-title') }}
									</h2>
									<p
										class="font-semibold w-full md:w-1/2 sm:w-2/3 leading-normal text-xs sm:text-2xl drop-shadow-lg "
										:class="slide.source.subtitleClass"
									>
										{{ $t('home.hero-sub-title') }}
									</p>
									<Button
										as="a"
										href=""
										variant="white"
										class="max-w-28 h-6 px-2 sm:h-10 sm:max-w-44 drop-shadow-lg"
									>
										<span class="text-xs sm:text-lg font-semibold">{{ $t('home.widget-pay-now') }}</span>
									</Button>
								</div>
							</div>
							<NuxtImg
								:alt="$t('app.marketing-alt')"
								:title="$t('app.marketing-alt')"
								quality="90"
								:preload="!index ? { fetchPriority: 'high' } : false"
								loading="eager"
								format="webp"
								sizes="(max-width: 599px) 100vw, (max-width: 991px) 100vw, 100vw"
								:srcset="`${img(slide.source.sm, { width: 800 })} 800w, ${img(slide.source.md, { width: 1200 })} 1200w, ${img(slide.source.lg, { width: 1800 })} 1800w`"
								class="object-cover object-start w-full h-full"
							/>
						</CardContent>
					</Card>
				</CarouselItem>
			</CarouselContent>
			<div class="flex absolute bottom-0 w-full h-8 justify-center items-center gap-2 pointer-events-none">
				<div
					v-for="(_, iIndex) in list"
					:key="`scroll-thumb-image-${iIndex}`"
					class="flex p-1 rounded-full bg-gray-300 h-1 transition ease-in-out duration-200 pointer-events-auto"
					:data-index="iIndex"
					:class="{ 'bg-primary-600 px-4': iIndex === selectedIndex }"
					@click.prevent="onThumbClick(iIndex)"
				/>
			</div>
		</Carousel>
	</div>
</template>
